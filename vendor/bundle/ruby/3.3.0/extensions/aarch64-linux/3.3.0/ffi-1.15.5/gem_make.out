current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/ffi-1.15.5/ext/ffi_c
/usr/local/bin/ruby extconf.rb
checking for pkg-config for libffi... [" ", "", "-lffi"]
checking for ffi_prep_closure_loc() in -lffi... yes
checking for ffi_prep_cif_var()... yes
checking for ffi_raw_call()... yes
checking for ffi_prep_raw_closure()... yes
checking for whether -pthread is accepted as LDFLAGS... yes
creating extconf.h
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/ffi-1.15.5/ext/ffi_c
make DESTDIR\= sitearchdir\=./.gem.20250731-10-d3d9j2 sitelibdir\=./.gem.20250731-10-d3d9j2 clean

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/ffi-1.15.5/ext/ffi_c
make DESTDIR\= sitearchdir\=./.gem.20250731-10-d3d9j2 sitelibdir\=./.gem.20250731-10-d3d9j2
compiling AbstractMemory.c
compiling ArrayType.c
compiling Buffer.c
compiling Call.c
compiling ClosurePool.c
compiling DynamicLibrary.c
compiling Function.c
compiling FunctionInfo.c
compiling LastError.c
compiling LongDouble.c
compiling MappedType.c
compiling MemoryPointer.c
compiling MethodHandle.c
compiling Platform.c
compiling Pointer.c
compiling Struct.c
compiling StructByValue.c
compiling StructLayout.c
compiling Thread.c
compiling Type.c
compiling Types.c
compiling Variadic.c
compiling ffi.c
linking shared-object ffi_c.so

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/ffi-1.15.5/ext/ffi_c
make DESTDIR\= sitearchdir\=./.gem.20250731-10-d3d9j2 sitelibdir\=./.gem.20250731-10-d3d9j2 install
/usr/bin/install -c -m 0755 ffi_c.so ./.gem.20250731-10-d3d9j2

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/ffi-1.15.5/ext/ffi_c
make DESTDIR\= sitearchdir\=./.gem.20250731-10-d3d9j2 sitelibdir\=./.gem.20250731-10-d3d9j2 clean
