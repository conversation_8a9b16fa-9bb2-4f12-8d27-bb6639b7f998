current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bindex-0.8.1/ext/skiptrace
/usr/local/bin/ruby extconf.rb
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bindex-0.8.1/ext/skiptrace
make DESTDIR\= sitearchdir\=./.gem.20250731-10-4fx95m sitelibdir\=./.gem.20250731-10-4fx95m clean

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bindex-0.8.1/ext/skiptrace
make DESTDIR\= sitearchdir\=./.gem.20250731-10-4fx95m sitelibdir\=./.gem.20250731-10-4fx95m
compiling cruby.c
linking shared-object skiptrace/internal/cruby.so

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bindex-0.8.1/ext/skiptrace
make DESTDIR\= sitearchdir\=./.gem.20250731-10-4fx95m sitelibdir\=./.gem.20250731-10-4fx95m install
/usr/bin/install -c -m 0755 cruby.so ./.gem.20250731-10-4fx95m/skiptrace/internal

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bindex-0.8.1/ext/skiptrace
make DESTDIR\= sitearchdir\=./.gem.20250731-10-4fx95m sitelibdir\=./.gem.20250731-10-4fx95m clean
