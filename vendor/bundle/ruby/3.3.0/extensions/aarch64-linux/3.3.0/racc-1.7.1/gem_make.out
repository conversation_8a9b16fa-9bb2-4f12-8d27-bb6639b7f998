current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/racc-1.7.1/ext/racc/cparse
/usr/local/bin/ruby extconf.rb
checking for rb_block_call()... yes
checking for rb_ary_subseq()... yes
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/racc-1.7.1/ext/racc/cparse
make DESTDIR\= sitearchdir\=./.gem.20250731-10-q894kb sitelibdir\=./.gem.20250731-10-q894kb clean

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/racc-1.7.1/ext/racc/cparse
make DESTDIR\= sitearchdir\=./.gem.20250731-10-q894kb sitelibdir\=./.gem.20250731-10-q894kb
compiling cparse.c
linking shared-object racc/cparse.so

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/racc-1.7.1/ext/racc/cparse
make DESTDIR\= sitearchdir\=./.gem.20250731-10-q894kb sitelibdir\=./.gem.20250731-10-q894kb install
/usr/bin/install -c -m 0755 cparse.so ./.gem.20250731-10-q894kb/racc

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/racc-1.7.1/ext/racc/cparse
make DESTDIR\= sitearchdir\=./.gem.20250731-10-q894kb sitelibdir\=./.gem.20250731-10-q894kb clean
