current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/puma-6.6.0/ext/puma_http11
/usr/local/bin/ruby extconf.rb
checking for pkg-config for openssl... [" ", "", "-lssl -lcrypto"]
──── Using OpenSSL pkgconfig (openssl.pc) ────
checking for openssl/bio.h... yes

──── Below are yes for 1.0.2 & later ────
checking for DTLS_method() in openssl/ssl.h... yes
checking for SSL_CTX_set_session_cache_mode(NULL, 0) in openssl/ssl.h... yes

──── Below are yes for 1.1.0 & later ────
checking for TLS_server_method() in openssl/ssl.h... yes
checking for SSL_CTX_set_min_proto_version(NULL, 0) in openssl/ssl.h... yes

──── Below is yes for 1.1.0 and later, but isn't documented until 3.0.0 ────
checking for SSL_CTX_set_dh_auto(NULL, 0) in openssl/ssl.h... yes

──── Below is yes for 1.1.1 & later ────
checking for SSL_CTX_set_ciphersuites(NULL, "") in openssl/ssl.h... yes

──── Below is yes for 3.0.0 & later ────
checking for SSL_get1_peer_certificate() in openssl/ssl.h... yes

checking for Random.bytes... yes
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/puma-6.6.0/ext/puma_http11
make DESTDIR\= sitearchdir\=./.gem.20250731-10-kr32bu sitelibdir\=./.gem.20250731-10-kr32bu clean
make: Warning: File 'Makefile' has modification time 0.02 s in the future
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/puma-6.6.0/ext/puma_http11
make DESTDIR\= sitearchdir\=./.gem.20250731-10-kr32bu sitelibdir\=./.gem.20250731-10-kr32bu
compiling http11_parser.c
ext/puma_http11/http11_parser.c: In function ‘puma_parser_execute’:
ext/puma_http11/http11_parser.c:436:19: warning: comparison is always true due to limited range of data type [-Wtype-limits]
ext/puma_http11/http11_parser.c:455:19: warning: comparison is always true due to limited range of data type [-Wtype-limits]
ext/puma_http11/http11_parser.c:510:19: warning: comparison is always true due to limited range of data type [-Wtype-limits]
ext/puma_http11/http11_parser.c:531:19: warning: comparison is always true due to limited range of data type [-Wtype-limits]
ext/puma_http11/http11_parser.c:576:9: warning: comparison is always true due to limited range of data type [-Wtype-limits]
ext/puma_http11/http11_parser.c:597:9: warning: comparison is always true due to limited range of data type [-Wtype-limits]
ext/puma_http11/http11_parser.c:619:9: warning: comparison is always true due to limited range of data type [-Wtype-limits]
ext/puma_http11/http11_parser.c:639:9: warning: comparison is always true due to limited range of data type [-Wtype-limits]
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
compiling mini_ssl.c
compiling puma_http11.c
linking shared-object puma/puma_http11.so

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/puma-6.6.0/ext/puma_http11
make DESTDIR\= sitearchdir\=./.gem.20250731-10-kr32bu sitelibdir\=./.gem.20250731-10-kr32bu install
make: Warning: File 'puma_http11.so' has modification time 0.023 s in the future
/usr/bin/install -c -m 0755 puma_http11.so ./.gem.20250731-10-kr32bu/puma
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/puma-6.6.0/ext/puma_http11
make DESTDIR\= sitearchdir\=./.gem.20250731-10-kr32bu sitelibdir\=./.gem.20250731-10-kr32bu clean
