current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/date-3.3.3/ext/date
/usr/local/bin/ruby extconf.rb
checking for rb_category_warn()... yes
checking for timezone in time.h with  -Werror... yes
checking for altzone in time.h with  -Werror... no
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/date-3.3.3/ext/date
make DESTDIR\= sitearchdir\=./.gem.20250731-10-80luse sitelibdir\=./.gem.20250731-10-80luse clean

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/date-3.3.3/ext/date
make DESTDIR\= sitearchdir\=./.gem.20250731-10-80luse sitelibdir\=./.gem.20250731-10-80luse
compiling date_core.c
compiling date_parse.c
compiling date_strftime.c
compiling date_strptime.c
linking shared-object date_core.so

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/date-3.3.3/ext/date
make DESTDIR\= sitearchdir\=./.gem.20250731-10-80luse sitelibdir\=./.gem.20250731-10-80luse install
/usr/bin/install -c -m 0755 date_core.so ./.gem.20250731-10-80luse

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/date-3.3.3/ext/date
make DESTDIR\= sitearchdir\=./.gem.20250731-10-80luse sitelibdir\=./.gem.20250731-10-80luse clean
