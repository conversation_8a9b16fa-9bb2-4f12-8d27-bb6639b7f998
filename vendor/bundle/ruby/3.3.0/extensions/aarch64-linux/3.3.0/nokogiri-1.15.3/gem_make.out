current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ext/nokogiri
/usr/local/bin/ruby extconf.rb
checking for whether -std=c99 is accepted as CFLAGS... yes
checking for whether -Wno-declaration-after-statement is accepted as CFLAGS... yes
checking for whether -O2 is accepted as CFLAGS... yes
checking for whether -g is accepted as CFLAGS... yes
checking for whether -Winline is accepted as CFLAGS... yes
checking for whether -Wmissing-noreturn is accepted as CFLAGS... yes
checking for whether -Wconversion -Wno-sign-conversion is accepted as CFLAGS... yes
Building nokogiri using packaged libraries.
Static linking is enabled.
Cross build is disabled.
Using mini_portile version 2.8.2
checking for iconv... yes
---------- IMPORTANT NOTICE ----------
Building Nokogiri with a packaged version of libxml2-2.11.4.
Configuration options: --host\=aarch64-unknown-linux --enable-static --disable-shared --libdir\=/smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ports/aarch64-linux/libxml2/2.11.4/lib --with-iconv\=yes --disable-dependency-tracking --without-python --without-readline --with-c14n --with-debug --with-threads --disable-shared --enable-static CPPFLAGS\= CFLAGS\=-O2\ -U_FORTIFY_SOURCE\ -g\ -fPIC
The following patches are being applied:
  - 0001-Remove-script-macro-support.patch
  - 0002-Update-entities-to-remove-handling-of-ssi.patch
  - 0003-libxml2.la-is-in-top_builddir.patch
  - 0009-allow-wildcard-namespaces.patch
  - 0010-update-config.guess-and-config.sub-for-libxml2.patch
  - 0011-rip-out-libxml2-s-libc_single_threaded-support.patch

The Nokogiri maintainers intend to provide timely security updates, but if
this is a concern for you and want to use your OS/distro system library
instead, then abort this installation process and install nokogiri as
instructed at:

  https://nokogiri.org/tutorials/installing_nokogiri.html#installing-using-standard-system-libraries

Note, however, that nokogiri cannot guarantee compatibility with every
version of libxml2 that may be provided by OS/package vendors.

Extracting libxml2-2.11.4.tar.xz into tmp/aarch64-unknown-linux/ports/libxml2/2.11.4... OK
Running git apply with /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/patches/libxml2/0001-Remove-script-macro-support.patch... OK
Running git apply with /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/patches/libxml2/0002-Update-entities-to-remove-handling-of-ssi.patch... OK
Running git apply with /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/patches/libxml2/0003-libxml2.la-is-in-top_builddir.patch... OK
Running git apply with /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/patches/libxml2/0009-allow-wildcard-namespaces.patch... OK
Running git apply with /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/patches/libxml2/0010-update-config.guess-and-config.sub-for-libxml2.patch... OK
Running git apply with /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/patches/libxml2/0011-rip-out-libxml2-s-libc_single_threaded-support.patch... OK
Running 'configure' for libxml2 2.11.4... OK
Running 'compile' for libxml2 2.11.4... OK
Running 'install' for libxml2 2.11.4... OK
Activating libxml2 2.11.4 (from /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ports/aarch64-linux/libxml2/2.11.4)...
Using mini_portile version 2.8.2
---------- IMPORTANT NOTICE ----------
Building Nokogiri with a packaged version of libxslt-1.1.38.
Configuration options: --host\=aarch64-unknown-linux --enable-static --disable-shared --libdir\=/smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ports/aarch64-linux/libxslt/1.1.38/lib --disable-dependency-tracking --without-python --without-crypto --with-debug --with-libxml-prefix\=/smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ports/aarch64-linux/libxml2/2.11.4 --disable-shared --enable-static CFLAGS\=-O2\ -U_FORTIFY_SOURCE\ -g\ -fPIC
The following patches are being applied:
  - 0001-update-config.guess-and-config.sub-for-libxslt.patch

The Nokogiri maintainers intend to provide timely security updates, but if
this is a concern for you and want to use your OS/distro system library
instead, then abort this installation process and install nokogiri as
instructed at:

  https://nokogiri.org/tutorials/installing_nokogiri.html#installing-using-standard-system-libraries

Extracting libxslt-1.1.38.tar.xz into tmp/aarch64-unknown-linux/ports/libxslt/1.1.38... OK
Running git apply with /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/patches/libxslt/0001-update-config.guess-and-config.sub-for-libxslt.patch... OK
Running 'configure' for libxslt 1.1.38... OK
Running 'compile' for libxslt 1.1.38... OK
Running 'install' for libxslt 1.1.38... OK
Activating libxslt 1.1.38 (from /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ports/aarch64-linux/libxslt/1.1.38)...
checking for whether -DNOKOGIRI_PACKAGED_LIBRARIES is accepted as CPPFLAGS... yes
xml2-config cflags: -I/smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ports/aarch64-linux/libxml2/2.11.4/include/libxml2
checking for whether -DNOKOGIRI_LIBXML2_PATCHES="\"0001-Remove-script-macro-support.patch 0002-Update-entities-to-remove-handling-of-ssi.patch 0003-libxml2.la-is-in-top_builddir.patch 0009-allow-wildcard-namespaces.patch 0010-update-config.guess-and-config.sub-for-libxml2.patch 0011-rip-out-libxml2-s-libc_single_threaded-support.patch\"" is accepted as CPPFLAGS... yes
checking for -llzma... yes
xslt-config cflags: -I/smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ports/aarch64-linux/libxml2/2.11.4/include/libxml2 -I/smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ports/aarch64-linux/libxslt/1.1.38/include
checking for whether -DNOKOGIRI_LIBXSLT_PATCHES="\"0001-update-config.guess-and-config.sub-for-libxslt.patch\"" is accepted as CPPFLAGS... yes
checking for xmlParseDoc() in libxml/parser.h... yes
checking for xsltParseStylesheetDoc() in libxslt/xslt.h... yes
checking for exsltFuncRegister() in libexslt/exslt.h... yes
Using mini_portile version 2.8.2
---------- IMPORTANT NOTICE ----------
Building Nokogiri with a packaged version of libgumbo-1.0.0-nokogiri.
Configuration options: --disable-shared --enable-static CFLAGS\=-fPIC
Copying gumbo-parser files into tmp/aarch64-unknown-linux/ports/libgumbo/1.0.0-nokogiri/gumbo-parser...
Running 'compile' for libgumbo 1.0.0-nokogiri... OK
Activating libgumbo 1.0.0-nokogiri (from ports/aarch64-linux/libgumbo/1.0.0-nokogiri)...
checking for whether -I/smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ext/nokogiri/ports/aarch64-linux/libgumbo/1.0.0-nokogiri/include is accepted as CPPFLAGS... yes
checking for gumbo_parse_with_options() in nokogiri_gumbo.h... yes
checking for xmlHasFeature()... yes
checking for xmlFirstElementChild()... yes
checking for xmlRelaxNGSetParserStructuredErrors()... yes
checking for xmlRelaxNGSetValidStructuredErrors()... yes
checking for xmlSchemaSetValidStructuredErrors()... yes
checking for xmlSchemaSetParserStructuredErrors()... yes
checking for rb_gc_location()... yes
checking for rb_category_warning()... yes
checking for whether -DNOKOGIRI_OTHER_LIBRARY_VERSIONS="\"libgumbo:1.0.0-nokogiri\"" is accepted as CPPFLAGS... yes
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ext/nokogiri
make DESTDIR\= sitearchdir\=./.gem.20250731-10-qvktc2 sitelibdir\=./.gem.20250731-10-qvktc2 clean

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ext/nokogiri
make DESTDIR\= sitearchdir\=./.gem.20250731-10-qvktc2 sitelibdir\=./.gem.20250731-10-qvktc2
compiling gumbo.c
compiling html4_document.c
compiling html4_element_description.c
compiling html4_entity_lookup.c
compiling html4_sax_parser_context.c
compiling html4_sax_push_parser.c
compiling libxml2_backwards_compat.c
compiling nokogiri.c
compiling test_global_handlers.c
compiling xml_attr.c
compiling xml_attribute_decl.c
compiling xml_cdata.c
compiling xml_comment.c
compiling xml_document.c
compiling xml_document_fragment.c
compiling xml_dtd.c
compiling xml_element_content.c
compiling xml_element_decl.c
compiling xml_encoding_handler.c
compiling xml_entity_decl.c
compiling xml_entity_reference.c
compiling xml_namespace.c
compiling xml_node.c
compiling xml_node_set.c
compiling xml_processing_instruction.c
compiling xml_reader.c
compiling xml_relax_ng.c
compiling xml_sax_parser.c
compiling xml_sax_parser_context.c
compiling xml_sax_push_parser.c
compiling xml_schema.c
compiling xml_syntax_error.c
compiling xml_text.c
compiling xml_xpath_context.c
compiling xslt_stylesheet.c
linking shared-object nokogiri/nokogiri.so
Cleaning files only used during build.

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ext/nokogiri
make DESTDIR\= sitearchdir\=./.gem.20250731-10-qvktc2 sitelibdir\=./.gem.20250731-10-qvktc2 install
/usr/bin/install -c -m 0755 nokogiri.so ./.gem.20250731-10-qvktc2/nokogiri
installing nokogiri libraries

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/nokogiri-1.15.3/ext/nokogiri
make DESTDIR\= sitearchdir\=./.gem.20250731-10-qvktc2 sitelibdir\=./.gem.20250731-10-qvktc2 clean
