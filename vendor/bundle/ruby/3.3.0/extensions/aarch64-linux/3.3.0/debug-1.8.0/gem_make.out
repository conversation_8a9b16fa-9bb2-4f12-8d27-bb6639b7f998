current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/debug-1.8.0/ext/debug
/usr/local/bin/ruby extconf.rb
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/debug-1.8.0/ext/debug
make DESTDIR\= sitearchdir\=./.gem.20250731-10-1ed9qy sitelibdir\=./.gem.20250731-10-1ed9qy clean
make: Warning: File 'Makefile' has modification time 0.024 s in the future
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/debug-1.8.0/ext/debug
make DESTDIR\= sitearchdir\=./.gem.20250731-10-1ed9qy sitelibdir\=./.gem.20250731-10-1ed9qy
compiling debug.c
compiling iseq_collector.c
linking shared-object debug/debug.so

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/debug-1.8.0/ext/debug
make DESTDIR\= sitearchdir\=./.gem.20250731-10-1ed9qy sitelibdir\=./.gem.20250731-10-1ed9qy install
make: Warning: File 'debug.so' has modification time 0.019 s in the future
/usr/bin/install -c -m 0755 debug.so ./.gem.20250731-10-1ed9qy/debug
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/debug-1.8.0/ext/debug
make DESTDIR\= sitearchdir\=./.gem.20250731-10-1ed9qy sitelibdir\=./.gem.20250731-10-1ed9qy clean
