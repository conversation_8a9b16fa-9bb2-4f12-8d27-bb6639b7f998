current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.16.0/ext/bootsnap
/usr/local/bin/ruby extconf.rb
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.16.0/ext/bootsnap
make DESTDIR\= sitearchdir\=./.gem.20250731-10-5v9ex4 sitelibdir\=./.gem.20250731-10-5v9ex4 clean
make: Warning: File 'Makefile' has modification time 0.02 s in the future
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.16.0/ext/bootsnap
make DESTDIR\= sitearchdir\=./.gem.20250731-10-5v9ex4 sitelibdir\=./.gem.20250731-10-5v9ex4
compiling bootsnap.c
linking shared-object bootsnap/bootsnap.so

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.16.0/ext/bootsnap
make DESTDIR\= sitearchdir\=./.gem.20250731-10-5v9ex4 sitelibdir\=./.gem.20250731-10-5v9ex4 install
/usr/bin/install -c -m 0755 bootsnap.so ./.gem.20250731-10-5v9ex4/bootsnap

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bootsnap-1.16.0/ext/bootsnap
make DESTDIR\= sitearchdir\=./.gem.20250731-10-5v9ex4 sitelibdir\=./.gem.20250731-10-5v9ex4 clean
