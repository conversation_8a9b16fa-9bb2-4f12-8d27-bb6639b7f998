current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bcrypt-3.1.19/ext/mri
/usr/local/bin/ruby extconf.rb
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bcrypt-3.1.19/ext/mri
make DESTDIR\= sitearchdir\=./.gem.20250731-10-tevrct sitelibdir\=./.gem.20250731-10-tevrct clean

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bcrypt-3.1.19/ext/mri
make DESTDIR\= sitearchdir\=./.gem.20250731-10-tevrct sitelibdir\=./.gem.20250731-10-tevrct
compiling bcrypt_ext.c
bcrypt_ext.c: In function ‘Init_bcrypt_ext’:
bcrypt_ext.c:113:6: warning: old-style function definition [-Wold-style-definition]
  113 | void Init_bcrypt_ext(){
      |      ^~~~~~~~~~~~~~~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
compiling crypt_blowfish.c
gcc  -D__SKIP_GNU     -c -o x86.o x86.S
compiling crypt_gensalt.c
compiling wrapper.c
wrapper.c:182:60: warning: ‘struct crypt_data’ declared inside parameter list will not be visible outside of this definition or declaration
  182 | char *crypt_r(const char *key, const char *setting, struct crypt_data *data)
      |                                                            ^~~~~~~~~~
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
linking shared-object bcrypt_ext.so

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bcrypt-3.1.19/ext/mri
make DESTDIR\= sitearchdir\=./.gem.20250731-10-tevrct sitelibdir\=./.gem.20250731-10-tevrct install
/usr/bin/install -c -m 0755 bcrypt_ext.so ./.gem.20250731-10-tevrct

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/bcrypt-3.1.19/ext/mri
make DESTDIR\= sitearchdir\=./.gem.20250731-10-tevrct sitelibdir\=./.gem.20250731-10-tevrct clean
