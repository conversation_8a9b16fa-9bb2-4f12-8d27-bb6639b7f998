current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/websocket-driver-0.7.5/ext/websocket-driver
/usr/local/bin/ruby extconf.rb
creating Makefile

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/websocket-driver-0.7.5/ext/websocket-driver
make DESTDIR\= sitearchdir\=./.gem.20250731-10-879ze sitelibdir\=./.gem.20250731-10-879ze clean
make: Warning: File 'Makefile' has modification time 0.0083 s in the future
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/websocket-driver-0.7.5/ext/websocket-driver
make DESTDIR\= sitearchdir\=./.gem.20250731-10-879ze sitelibdir\=./.gem.20250731-10-879ze
compiling websocket_mask.c
websocket_mask.c: In function ‘Init_websocket_mask’:
websocket_mask.c:26:6: warning: old-style function definition [-Wold-style-definition]
   26 | void Init_websocket_mask()
      |      ^~~~~~~~~~~~~~~~~~~
At top level:
cc1: note: unrecognized command-line option ‘-Wno-self-assign’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-parentheses-equality’ may have been intended to silence earlier diagnostics
cc1: note: unrecognized command-line option ‘-Wno-constant-logical-operand’ may have been intended to silence earlier diagnostics
linking shared-object websocket_mask.so

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/websocket-driver-0.7.5/ext/websocket-driver
make DESTDIR\= sitearchdir\=./.gem.20250731-10-879ze sitelibdir\=./.gem.20250731-10-879ze install
make: Warning: File 'websocket_mask.so' has modification time 0.014 s in the future
/usr/bin/install -c -m 0755 websocket_mask.so ./.gem.20250731-10-879ze
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /smart_alliance/vendor/bundle/ruby/3.3.0/gems/websocket-driver-0.7.5/ext/websocket-driver
make DESTDIR\= sitearchdir\=./.gem.20250731-10-879ze sitelibdir\=./.gem.20250731-10-879ze clean
