# frozen_string_literal: true

module ActionCable
  module Connection
    extend ActiveSupport::Autoload

    eager_autoload do
      autoload :Authorization
      autoload :Base
      autoload :ClientSocket
      autoload :Identification
      autoload :InternalChannel
      autoload :MessageBuffer
      autoload :Stream
      autoload :StreamEvent<PERSON>oop
      autoload :Subscriptions
      autoload :TaggedLoggerProxy
      autoload :TestCase
      autoload :WebSocket
    end
  end
end
