{"name": "@rails/actiontext", "version": "7.0.6", "description": "Edit and display rich text in Rails applications", "main": "app/assets/javascripts/actiontext.js", "type": "module", "files": ["app/assets/javascripts/*.js"], "homepage": "https://rubyonrails.org/", "repository": {"type": "git", "url": "git+https://github.com/rails/rails.git"}, "bugs": {"url": "https://github.com/rails/rails/issues"}, "author": "Basecamp, LLC", "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "dependencies": {"@rails/activestorage": ">= 7.0.0-alpha1"}, "peerDependencies": {"trix": "^1.3.1"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.0.1", "@rollup/plugin-commonjs": "^19.0.1", "rollup": "^2.35.1", "trix": "^1.3.1"}, "scripts": {"build": "rollup --config rollup.config.js"}}