<header role="banner">
  <h1>
    <%= @exception.cause.class.to_s %> in
    <%= @request.parameters["controller"].camelize if @request.parameters["controller"] %>#<%= @request.parameters["action"] %>
  </h1>
</header>

<main role="main" id="container">
  <p>
    Showing <i><%= @exception.file_name %></i> where line <b>#<%= @exception.line_number %></b> raised:
  </p>
  <pre><code><%= h @exception.message %></code></pre>

  <%= render "rescues/source", source_extracts: @source_extracts, show_source_idx: @show_source_idx %>

  <p><%= @exception.sub_template_message %></p>

  <%= render "rescues/trace", traces: @traces, trace_to_show: @trace_to_show %>
  <%= render template: "rescues/_request_and_response" %>
</main>
