# frozen_string_literal: true

require "uri"
require "active_support/core_ext/hash/indifferent_access"
require "active_support/core_ext/string/access"
require "action_controller/metal/exceptions"

module ActionDispatch
  module Assertions
    # Suite of assertions to test routes generated by \Rails and the handling of requests made to them.
    module RoutingAssertions
      def setup # :nodoc:
        @routes ||= nil
        super
      end

      # Asserts that the routing of the given +path+ was handled correctly and that the parsed options (given in the +expected_options+ hash)
      # match +path+. Basically, it asserts that \Rails recognizes the route given by +expected_options+.
      #
      # Pass a hash in the second argument (+path+) to specify the request method. This is useful for routes
      # requiring a specific HTTP method. The hash should contain a +:path+ with the incoming request path
      # and a +:method+ containing the required HTTP verb.
      #
      #   # Asserts that POSTing to /items will call the create action on ItemsController
      #   assert_recognizes({controller: 'items', action: 'create'}, {path: 'items', method: :post})
      #
      # You can also pass in +extras+ with a hash containing URL parameters that would normally be in the query string. This can be used
      # to assert that values in the query string will end up in the params hash correctly. To test query strings you must use the extras
      # argument because appending the query string on the path directly will not work. For example:
      #
      #   # Asserts that a path of '/items/list/1?view=print' returns the correct options
      #   assert_recognizes({controller: 'items', action: 'list', id: '1', view: 'print'}, 'items/list/1', { view: "print" })
      #
      # The +message+ parameter allows you to pass in an error message that is displayed upon failure.
      #
      #   # Check the default route (i.e., the index action)
      #   assert_recognizes({controller: 'items', action: 'index'}, 'items')
      #
      #   # Test a specific action
      #   assert_recognizes({controller: 'items', action: 'list'}, 'items/list')
      #
      #   # Test an action with a parameter
      #   assert_recognizes({controller: 'items', action: 'destroy', id: '1'}, 'items/destroy/1')
      #
      #   # Test a custom route
      #   assert_recognizes({controller: 'items', action: 'show', id: '1'}, 'view/item1')
      def assert_recognizes(expected_options, path, extras = {}, msg = nil)
        if path.is_a?(Hash) && path[:method].to_s == "all"
          [:get, :post, :put, :delete].each do |method|
            assert_recognizes(expected_options, path.merge(method: method), extras, msg)
          end
        else
          request = recognized_request_for(path, extras, msg)

          expected_options = expected_options.clone

          expected_options.stringify_keys!

          msg = message(msg, "") {
            sprintf("The recognized options <%s> did not match <%s>, difference:",
                    request.path_parameters, expected_options)
          }

          assert_equal(expected_options, request.path_parameters, msg)
        end
      end

      # Asserts that the provided options can be used to generate the provided path. This is the inverse of +assert_recognizes+.
      # The +extras+ parameter is used to tell the request the names and values of additional request parameters that would be in
      # a query string. The +message+ parameter allows you to specify a custom error message for assertion failures.
      #
      # The +defaults+ parameter is unused.
      #
      #   # Asserts that the default action is generated for a route with no action
      #   assert_generates "/items", controller: "items", action: "index"
      #
      #   # Tests that the list action is properly routed
      #   assert_generates "/items/list", controller: "items", action: "list"
      #
      #   # Tests the generation of a route with a parameter
      #   assert_generates "/items/list/1", { controller: "items", action: "list", id: "1" }
      #
      #   # Asserts that the generated route gives us our custom route
      #   assert_generates "changesets/12", { controller: 'scm', action: 'show_diff', revision: "12" }
      def assert_generates(expected_path, options, defaults = {}, extras = {}, message = nil)
        if %r{://}.match?(expected_path)
          fail_on(URI::InvalidURIError, message) do
            uri = URI.parse(expected_path)
            expected_path = uri.path.to_s.empty? ? "/" : uri.path
          end
        else
          expected_path = "/#{expected_path}" unless expected_path.start_with?("/")
        end

        options = options.clone
        generated_path, query_string_keys = @routes.generate_extras(options, defaults)
        found_extras = options.reject { |k, _| ! query_string_keys.include? k }

        msg = message || sprintf("found extras <%s>, not <%s>", found_extras, extras)
        assert_equal(extras, found_extras, msg)

        msg = message || sprintf("The generated path <%s> did not match <%s>", generated_path,
            expected_path)
        assert_equal(expected_path, generated_path, msg)
      end

      # Asserts that path and options match both ways; in other words, it verifies that <tt>path</tt> generates
      # <tt>options</tt> and then that <tt>options</tt> generates <tt>path</tt>. This essentially combines +assert_recognizes+
      # and +assert_generates+ into one step.
      #
      # The +extras+ hash allows you to specify options that would normally be provided as a query string to the action. The
      # +message+ parameter allows you to specify a custom error message to display upon failure.
      #
      #  # Asserts a basic route: a controller with the default action (index)
      #  assert_routing '/home', controller: 'home', action: 'index'
      #
      #  # Test a route generated with a specific controller, action, and parameter (id)
      #  assert_routing '/entries/show/23', controller: 'entries', action: 'show', id: 23
      #
      #  # Asserts a basic route (controller + default action), with an error message if it fails
      #  assert_routing '/store', { controller: 'store', action: 'index' }, {}, {}, 'Route for store index not generated properly'
      #
      #  # Tests a route, providing a defaults hash
      #  assert_routing 'controller/action/9', {id: "9", item: "square"}, {controller: "controller", action: "action"}, {}, {item: "square"}
      #
      #  # Tests a route with an HTTP method
      #  assert_routing({ method: 'put', path: '/product/321' }, { controller: "product", action: "update", id: "321" })
      def assert_routing(path, options, defaults = {}, extras = {}, message = nil)
        assert_recognizes(options, path, extras, message)

        controller, default_controller = options[:controller], defaults[:controller]
        if controller && controller.include?(?/) && default_controller && default_controller.include?(?/)
          options[:controller] = "/#{controller}"
        end

        generate_options = options.dup.delete_if { |k, _| defaults.key?(k) }
        assert_generates(path.is_a?(Hash) ? path[:path] : path, generate_options, defaults, extras, message)
      end

      # A helper to make it easier to test different route configurations.
      # This method temporarily replaces @routes with a new RouteSet instance.
      #
      # The new instance is yielded to the passed block. Typically the block
      # will create some routes using <tt>set.draw { match ... }</tt>:
      #
      #   with_routing do |set|
      #     set.draw do
      #       resources :users
      #     end
      #     assert_equal "/users", users_path
      #   end
      #
      def with_routing
        old_routes, @routes = @routes, ActionDispatch::Routing::RouteSet.new
        if defined?(@controller) && @controller
          old_controller, @controller = @controller, @controller.clone
          _routes = @routes

          @controller.singleton_class.include(_routes.url_helpers)

          if @controller.respond_to? :view_context_class
            view_context_class = Class.new(@controller.view_context_class) do
              include _routes.url_helpers
            end

            custom_view_context = Module.new {
              define_method(:view_context_class) do
                view_context_class
              end
            }
            @controller.extend(custom_view_context)
          end
        end
        yield @routes
      ensure
        @routes = old_routes
        if defined?(@controller) && @controller
          @controller = old_controller
        end
      end

      # ROUTES TODO: These assertions should really work in an integration context
      def method_missing(selector, *args, &block)
        if defined?(@controller) && @controller && defined?(@routes) && @routes && @routes.named_routes.route_defined?(selector)
          @controller.public_send(selector, *args, &block)
        else
          super
        end
      end
      ruby2_keywords(:method_missing)

      private
        # Recognizes the route for a given path.
        def recognized_request_for(path, extras = {}, msg)
          if path.is_a?(Hash)
            method = path[:method]
            path   = path[:path]
          else
            method = :get
          end

          controller = @controller if defined?(@controller)
          request = ActionController::TestRequest.create controller&.class

          if %r{://}.match?(path)
            fail_on(URI::InvalidURIError, msg) do
              uri = URI.parse(path)
              request.env["rack.url_scheme"] = uri.scheme || "http"
              request.host = uri.host if uri.host
              request.port = uri.port if uri.port
              request.path = uri.path.to_s.empty? ? "/" : uri.path
            end
          else
            path = "/#{path}" unless path.start_with?("/")
            request.path = path
          end

          request.request_method = method if method

          params = fail_on(ActionController::RoutingError, msg) do
            @routes.recognize_path(path, method: method, extras: extras)
          end
          request.path_parameters = params.with_indifferent_access

          request
        end

        def fail_on(exception_class, message)
          yield
        rescue exception_class => e
          raise Minitest::Assertion, message || e.message
        end
    end
  end
end
