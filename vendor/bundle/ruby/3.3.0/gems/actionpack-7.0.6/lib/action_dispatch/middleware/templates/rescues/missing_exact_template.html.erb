<header role="banner">
  <h1>No template for interactive request</h1>
</header>

<main id="container">
  <h2><%= h @exception.message %></h2>

  <p class="summary">
    <strong>NOTE!</strong><br>
    Unless told otherwise, Rails expects an action to render a template with the same name,<br>
    contained in a folder named after its controller.

    If this controller is an API responding with 204 (No Content), <br>
    which does not require a template,
    then this error will occur when trying to access it via browser,<br>
    since we expect an HTML template
    to be rendered for such requests. If that's the case, carry on.
  </p>
</main>
