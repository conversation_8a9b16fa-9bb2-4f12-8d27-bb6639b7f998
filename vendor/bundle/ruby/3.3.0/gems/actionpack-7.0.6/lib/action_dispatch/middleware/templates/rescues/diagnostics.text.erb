<%= @exception.class.to_s %><%
  if params_valid? && @request.parameters['controller']
%> in <%= @request.parameters['controller'].camelize %>Controller<% if @request.parameters['action'] %>#<%= @request.parameters['action'] %><% end %>
<% end %>

<%= @exception.message %>
<%= render template: "rescues/_source" %>
<%= render template: "rescues/_trace" %>
<%= render template: "rescues/_request_and_response" %>
