<%= @exception.class.to_s %><%
  if @request.parameters['controller']
%> in <%= @request.parameters['controller'].camelize %>Controller<% if @request.parameters['action'] %>#<%= @request.parameters['action'] %><% end %>
<% end %>

<%= @exception.message %>
<% if defined?(ActiveStorage) && @exception.message.match?(%r{#{ActiveStorage::Blob.table_name}|#{ActiveStorage::Attachment.table_name}}) %>
To resolve this issue run: bin/rails active_storage:install
<% end %>
<% if defined?(ActionMailbox) && @exception.message.match?(%r{#{ActionMailbox::InboundEmail.table_name}}) %>
To resolve this issue run: bin/rails action_mailbox:install
<% end %>

<%= render template: "rescues/_source" %>
<%= render template: "rescues/_trace" %>
<%= render template: "rescues/_request_and_response" %>
