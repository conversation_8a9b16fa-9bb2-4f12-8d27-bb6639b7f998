= Action Pack -- From request to response

Action Pack is a framework for handling and responding to web requests. It
provides mechanisms for *routing* (mapping request URLs to actions), defining
*controllers* that implement actions, and generating responses. In short, Action Pack
provides the controller layer in the MVC paradigm.

It consists of several modules:

* Action Dispatch, which parses information about the web request, handles
  routing as defined by the user, and does advanced processing related to HTTP
  such as MIME-type negotiation, decoding parameters in POST, PATCH, or PUT bodies,
  handling HTTP caching logic, cookies and sessions.

* Action Controller, which provides a base controller class that can be
  subclassed to implement filters and actions to handle requests. The result
  of an action is typically content generated from views.

With the Ruby on Rails framework, users only directly interface with the
Action Controller module. Necessary Action Dispatch functionality is activated
by default and Action View rendering is implicitly triggered by Action
Controller. However, these modules are designed to function on their own and
can be used outside of Rails.

You can read more about Action Pack in the {Action Controller Overview}[https://guides.rubyonrails.org/action_controller_overview.html] guide.

== Download and installation

The latest version of Action Pack can be installed with RubyGems:

  $ gem install actionpack

Source code can be downloaded as part of the \Rails project on GitHub:

* https://github.com/rails/rails/tree/main/actionpack


== License

Action Pack is released under the MIT license:

* https://opensource.org/licenses/MIT


== Support

API documentation is at:

* https://api.rubyonrails.org

Bug reports for the Ruby on \Rails project can be filed here:

* https://github.com/rails/rails/issues

Feature requests should be discussed on the rails-core mailing list here:

* https://discuss.rubyonrails.org/c/rubyonrails-core
